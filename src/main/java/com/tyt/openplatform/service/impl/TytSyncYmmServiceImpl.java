package com.tyt.openplatform.service.impl;

import com.alibaba.fastjson.JSON;
import com.tyt.openplatform.bean.MqYmmTransportMsg;
import com.tyt.openplatform.bean.YmmTransport;
import com.tyt.openplatform.entity.Transport;
import com.tyt.openplatform.entity.TytTransportSyncYmm;
import com.tyt.openplatform.mapper.TytTransportSyncYmmMapper;
import com.tyt.openplatform.mb.enums.PlatformResponseEnum;
import com.tyt.openplatform.mb.response.base.OpenPlatformResponse;
import com.tyt.openplatform.mb.response.goods.GoodsInfo;
import com.tyt.openplatform.service.OpenApiService;
import com.tyt.openplatform.service.TytSyncYmmService;
import com.tyt.openplatform.utils.IdUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

@Service
@Slf4j
public class TytSyncYmmServiceImpl implements TytSyncYmmService {
    @Autowired
    private TytTransportSyncYmmMapper tytTransportSyncYmmMapper;
    @Autowired
    private OpenApiService openApiService;

    @Override
    public synchronized void sync(String message) {
        MqYmmTransportMsg mqYmmTransportMsg = JSON.parseObject(message, MqYmmTransportMsg.class);
        if (Objects.isNull(mqYmmTransportMsg) && Objects.isNull(mqYmmTransportMsg.getYmmTransport())) {
            log.info("tyt货源同步至ymm,mq接收到的货源数据为空");
            return;
        }
        Long srcMsgId = mqYmmTransportMsg.getSrcMsgId();
        Transport transport = tytTransportSyncYmmMapper.selectEffectiveTransport(srcMsgId);
        if (Objects.isNull(transport)) {
            log.info("tyt货源同步至ymm【srcMsgId={}】,无效货源", srcMsgId);
            return;
        }
        TytTransportSyncYmm tytTransportSyncYmm = tytTransportSyncYmmMapper.selectBySrcMsgId(srcMsgId);
        if (Objects.nonNull(tytTransportSyncYmm)) {
            log.info("tyt货源同步至ymm【srcMsgId={}】,该货源已同步至集团", srcMsgId);
            return;
        }
        //生成请求流水号
        String transportNumber = IdUtils.getIncreaseIdByLocalTime();
        //货源信息
        YmmTransport ymmTransport = mqYmmTransportMsg.getYmmTransport();
        ymmTransport.setOuterCargoId(transport.getSrcMsgId());
        ymmTransport.setPartnerSerialNo(transportNumber);
        log.info("tyt货源同步至ymm【srcMsgId={}】,请求流水号={}", srcMsgId, transportNumber);
        //调用集团发货接口
        OpenPlatformResponse<String> response = openApiService.syncTransport(ymmTransport);
        log.info("tyt货源同步至ymm【srcMsgId={}】,集团响应信息为={}", srcMsgId, JSON.toJSONString(response));
        if (response.getCode().equals(PlatformResponseEnum.PLATFORM_RESPONSE_SUCCESS.getCode())) {
            String data = response.getData();
            GoodsInfo goodsInfo = JSON.parseObject(data, GoodsInfo.class);
            tytTransportSyncYmm = new TytTransportSyncYmm();
            tytTransportSyncYmm.setCargoId(goodsInfo.getCargoId());
            tytTransportSyncYmm.setPartnerSerialNo(goodsInfo.getPartnerSerialNo());
            tytTransportSyncYmm.setTransportStatus(0);
            tytTransportSyncYmm.setSyncStatus(0);
            tytTransportSyncYmm.setSubCode(response.getCode());
            tytTransportSyncYmm.setSubCodeMsg("同步成功");
            tytTransportSyncYmm.setTrackingMsg(goodsInfo.getTrackingMsg());
        } else {
            tytTransportSyncYmm = new TytTransportSyncYmm();
            tytTransportSyncYmm.setTransportStatus(0);
            tytTransportSyncYmm.setSyncStatus(1);
            tytTransportSyncYmm.setSubCode(response.getSubCode());
            tytTransportSyncYmm.setSubCodeMsg(response.getMsg());
        }
        tytTransportSyncYmm.setPublishType(transport.getPublishType());
        tytTransportSyncYmm.setTransportNumber(transportNumber);
        tytTransportSyncYmm.setSrcMsgId(srcMsgId);
        tytTransportSyncYmm.setUserId(transport.getUserId());
        tytTransportSyncYmm.setCtime(new Date());
        tytTransportSyncYmmMapper.insert(tytTransportSyncYmm);


        //延迟判断如果同步成功之后货源撤销了，再调用撤销接口

    }
}
